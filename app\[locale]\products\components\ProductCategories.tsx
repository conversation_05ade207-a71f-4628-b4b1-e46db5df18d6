"use client"

import { motion } from "framer-motion"
import { useTranslations } from "@/hooks/useTranslations"
import { Layers, Brain, Cloud, GraduationCap, Code, Filter, Grid3X3, CheckCircle2 } from "lucide-react"

interface Category {
  id: string
  name: string
  count: number
}

interface ProductCategoriesProps {
  categories: Category[]
  selectedCategory: string
  onCategoryChange: (category: string) => void
}

// Simplified category configuration
const CATEGORY_CONFIG = {
  all: {
    icon: Layers,
    gradient: "from-slate-500 to-zinc-600",
    color: "#64748B",
    bgColor: "from-slate-50/80 to-zinc-50/40",
  },
  AI服务: {
    icon: Brain,
    gradient: "from-blue-500 to-indigo-600",
    color: "#3B82F6",
    bgColor: "from-blue-50/80 to-purple-50/40",
  },
  云计算: {
    icon: Cloud,
    gradient: "from-emerald-500 to-teal-600",
    color: "#10B981",
    bgColor: "from-emerald-50/80 to-teal-50/40",
  },
  教育科技: {
    icon: GraduationCap,
    gradient: "from-purple-500 to-fuchsia-600",
    color: "#8B5CF6",
    bgColor: "from-purple-50/80 to-fuchsia-50/40",
  },
  定制开发: {
    icon: Code,
    gradient: "from-orange-500 to-pink-600",
    color: "#F97316",
    bgColor: "from-orange-50/80 to-pink-50/40",
  },
} as const

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20, scale: 0.95 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 120,
      damping: 12,
    },
  },
}

function CategoryCard({
  category,
  isSelected,
  onClick,
  index,
}: {
  category: Category
  isSelected: boolean
  onClick: () => void
  index: number
}) {
  const t = useTranslations("productsPage")
  const config = CATEGORY_CONFIG[category.id as keyof typeof CATEGORY_CONFIG] || CATEGORY_CONFIG["all"]
  const IconComponent = config.icon

  return (
    <motion.div
      variants={itemVariants}
      whileHover={{ y: -8, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group relative"
    >
      {/* Glow effect for selected state */}
      {isSelected && (
        <motion.div
          className="absolute -inset-1 rounded-2xl opacity-30 blur-lg"
          style={{ background: `linear-gradient(135deg, ${config.gradient})` }}
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
        />
      )}

      <button
        onClick={onClick}
        className={`
          relative w-full p-6 rounded-2xl border-2 transition-all duration-300 backdrop-blur-sm
          ${
            isSelected
              ? "border-transparent text-white shadow-xl"
              : "border-white/30 bg-white/70 text-slate-700 hover:border-white/50 hover:bg-white/90 hover:shadow-lg"
          }
        `}
        style={
          isSelected
            ? {
                background: `linear-gradient(135deg, ${config.gradient})`,
              }
            : {}
        }
      >
        {/* Background pattern */}
        <div
          className={`
          absolute inset-0 rounded-2xl opacity-5 transition-opacity duration-300
          ${isSelected ? "opacity-10" : "group-hover:opacity-8"}
        `}
        >
          <div
            className="absolute inset-0 rounded-2xl"
            style={{ background: `linear-gradient(135deg, ${config.gradient})` }}
          />
        </div>

        <div className="relative z-10 text-center">
          {/* Icon */}
          <motion.div
            className={`
              inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 transition-all duration-300
              ${isSelected ? "bg-white/20 text-white" : "bg-white/60 group-hover:bg-white/80"}
            `}
            style={!isSelected ? { color: config.color } : {}}
            whileHover={{ rotate: [0, -5, 5, 0] }}
            transition={{ duration: 0.5 }}
          >
            <IconComponent className="w-6 h-6" />
          </motion.div>

          {/* Category name */}
          <h3
            className={`
            font-semibold text-lg mb-2 transition-colors duration-300
            ${isSelected ? "text-white" : "text-slate-800 group-hover:text-slate-900"}
          `}
          >
            {category.name}
          </h3>

          {/* Count */}
          <div className="flex items-center justify-center gap-1">
            <motion.div
              className={`
                flex items-center gap-1 px-3 py-1 rounded-lg text-xs font-medium transition-all duration-300
                ${isSelected ? "bg-white/20 text-white" : "bg-white/60 group-hover:bg-white/80"}
              `}
              style={!isSelected ? { color: config.color } : {}}
              whileHover={{ scale: 1.05 }}
            >
              <Grid3X3 className="w-3 h-3" />
              <span>{category.count}</span>
            </motion.div>
          </div>
        </div>

        {/* Selection indicator */}
        {isSelected && (
          <motion.div
            className="absolute top-4 right-4"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ type: "spring", stiffness: 200, damping: 15 }}
          >
            <div className="w-6 h-6 bg-white/25 rounded-full flex items-center justify-center">
              <CheckCircle2 className="w-4 h-4 text-white" />
            </div>
          </motion.div>
        )}
      </button>
    </motion.div>
  )
}

function SelectedCategoryInfo({
  selectedCategory,
  categories,
}: {
  selectedCategory: string
  categories: Category[]
}) {
  const selectedCat = categories.find((c) => c.id === selectedCategory)
  const config = CATEGORY_CONFIG[selectedCategory as keyof typeof CATEGORY_CONFIG] || CATEGORY_CONFIG["all"]

  if (!selectedCat) return null

  return (
    <motion.div
      className="flex items-center justify-center gap-4 px-6 py-3 rounded-2xl bg-white/80 backdrop-blur-sm border border-white/40 shadow-lg"
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ type: "spring", stiffness: 200, damping: 20 }}
    >
      <div className="flex items-center gap-2">
        <div className="w-3 h-3 rounded-full" style={{ background: `linear-gradient(135deg, ${config.gradient})` }} />
        <span className="text-sm font-medium text-slate-700">
          {selectedCategory === "all" ? "全部分类" : selectedCat.name}
        </span>
      </div>

      <div className="flex items-center gap-1 text-sm text-slate-600">
        <span>共</span>
        <span className="font-bold text-lg" style={{ color: config.color }}>
          {selectedCat.count}
        </span>
        <span>个产品</span>
      </div>
    </motion.div>
  )
}

export function ProductCategories({ categories, selectedCategory, onCategoryChange }: ProductCategoriesProps) {
  const t = useTranslations("productsPage")

  return (
    <section className="relative py-16 bg-gradient-to-br from-slate-50/50 via-white to-blue-50/30 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-100/20 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-indigo-100/15 rounded-full blur-3xl" />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-50/80 border border-blue-100/50 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <Filter className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">产品分类</span>
          </motion.div>

          <h2 className="text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-slate-900 to-blue-800 bg-clip-text text-transparent">
            {t("categories.title")}
          </h2>

          <p className="text-lg text-slate-600 max-w-2xl mx-auto">{t("categories.subtitle")}</p>
        </motion.div>

        {/* Categories grid */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-12"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {categories.map((category, index) => (
            <CategoryCard
              key={category.id}
              category={category}
              isSelected={selectedCategory === category.id}
              onClick={() => onCategoryChange(category.id)}
              index={index}
            />
          ))}
        </motion.div>

        {/* Selected category info */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <SelectedCategoryInfo selectedCategory={selectedCategory} categories={categories} />
        </motion.div>
      </div>
    </section>
  )
}
