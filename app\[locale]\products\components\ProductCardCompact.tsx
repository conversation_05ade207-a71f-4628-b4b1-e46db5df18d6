"use client"

import { useState, useMemo } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useTranslations } from "@/hooks/useTranslations"
import Link from "next/link"
import {
  ArrowRight,
  Star,
  TrendingUp,
  CheckCircle,
  Clock,
  Brain,
  Cpu,
  GraduationCap,
  Code,
  Globe,
  Database,
  Target,
  Eye,
  BarChart,
  Users,
  Settings,
  Award,
  BookOpen,
  Building,
  Smartphone,
  Shield,
  Zap,
} from "lucide-react"

// Types
interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: Array<{ text: string; iconName: string }>
  highlight?: string
  price?: string
  category: string
}

interface ProductCardProps {
  product: Product
  index: number
}

interface ThemeColors {
  primary: string
  accent: string
  light: string
  cardBg: string
}

// Constants
const ICON_MAP = {
  Brain,
  Cpu,
  GraduationCap,
  Code,
  Globe,
  Database,
  Target,
  Eye,
  BarChart,
  Users,
  Settings,
  Award,
  BookOpen,
  Building,
  Smartphone,
  Shield,
  Zap,
  CheckCircle,
  Star,
  <PERSON><PERSON>ding<PERSON><PERSON>,
  Clock,
} as const

const CATEGORY_THEMES: Record<string, ThemeColors> = {
  AI服务: {
    primary: "from-blue-500 via-blue-600 to-blue-700",
    accent: "#3B82F6",
    light: "rgba(59, 130, 246, 0.12)",
    cardBg: "from-blue-50/95 via-blue-25/80 to-white/90",
  },
  云计算: {
    primary: "from-blue-400 via-cyan-500 to-teal-600",
    accent: "#06B6D4",
    light: "rgba(6, 182, 212, 0.12)",
    cardBg: "from-cyan-50/95 via-blue-25/80 to-white/90",
  },
  教育科技: {
    primary: "from-blue-600 via-indigo-600 to-purple-600",
    accent: "#6366F1",
    light: "rgba(99, 102, 241, 0.12)",
    cardBg: "from-indigo-50/95 via-blue-25/80 to-white/90",
  },
  定制开发: {
    primary: "from-blue-500 via-slate-600 to-gray-700",
    accent: "#475569",
    light: "rgba(71, 85, 105, 0.12)",
    cardBg: "from-slate-50/95 via-blue-25/80 to-white/90",
  },
}

const DEFAULT_THEME: ThemeColors = {
  primary: "from-blue-500 via-blue-600 to-indigo-600",
  accent: "#3B82F6",
  light: "rgba(59, 130, 246, 0.12)",
  cardBg: "from-blue-50/95 via-blue-25/80 to-white/90",
}

// Animation variants
const cardVariants = {
  initial: { opacity: 0, y: 30, scale: 0.95 },
  animate: { opacity: 1, y: 0, scale: 1 },
  hover: {
    y: -8,
    scale: 1.02,
    transition: { duration: 0.3, ease: "easeOut" }
  },
}

const iconVariants = {
  hover: {
    scale: 1.1,
    rotate: 8,
    transition: { duration: 0.3, ease: "easeOut" }
  },
}

const titleVariants = {
  hover: {
    scale: 1.03,
    transition: { duration: 0.2, ease: "easeOut" }
  },
}

const tagVariants = {
  hover: {
    scale: 1.08,
    transition: { duration: 0.2, ease: "easeOut" }
  },
}

// Custom hooks
const useTheme = (category: string): ThemeColors => {
  return useMemo(() => CATEGORY_THEMES[category] || DEFAULT_THEME, [category])
}

// Components
const ProductIcon = ({
  iconName,
  className,
  theme,
}: {
  iconName: string
  className?: string
  theme: ThemeColors
}) => {
  const IconComponent = ICON_MAP[iconName as keyof typeof ICON_MAP]

  if (!IconComponent) {
    return <div className={cn("bg-blue-200 rounded-xl w-12 h-12", className)} />
  }

  return (
    <motion.div
      className="relative w-12 h-12 rounded-xl flex items-center justify-center shadow-lg overflow-hidden flex-shrink-0 border border-white/20"
      style={{
        background: `linear-gradient(135deg, ${theme.primary})`,
        boxShadow: `0 4px 20px ${theme.light}, 0 1px 3px rgba(0,0,0,0.1)`
      }}
      variants={iconVariants}
      whileHover="hover"
    >
      <IconComponent className={cn("w-6 h-6 text-white drop-shadow-sm", className)} />
      {/* Subtle shine effect */}
      <div className="absolute inset-0 bg-gradient-to-tr from-white/20 via-transparent to-transparent rounded-xl" />
    </motion.div>
  )
}

const CategoryTag = ({ category, theme }: { category: string; theme: ThemeColors }) => (
  <motion.span
    className="px-3 py-1 text-xs font-semibold rounded-full border flex-shrink-0 backdrop-blur-sm"
    style={{
      color: theme.accent,
      background: "rgba(255, 255, 255, 0.9)",
      borderColor: theme.accent + "40",
      boxShadow: `0 2px 8px ${theme.light}`,
    }}
    variants={tagVariants}
    whileHover="hover"
  >
    {category}
  </motion.span>
)

const FeatureItem = ({
  feature,
  theme,
  index,
}: {
  feature: { text: string; iconName: string }
  theme: ThemeColors
  index: number
}) => {
  const IconComponent = ICON_MAP[feature.iconName as keyof typeof ICON_MAP]

  return (
    <motion.div
      className="flex items-center gap-3 p-3 rounded-xl bg-white/70 backdrop-blur-sm border border-white/40 hover:bg-white/80 transition-colors duration-200"
      initial={{ opacity: 0, x: -15 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{
        duration: 0.4,
        delay: index * 0.15,
        ease: "easeOut"
      }}
      viewport={{ once: true }}
      whileHover={{ scale: 1.02 }}
    >
      <div
        className="w-6 h-6 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm"
        style={{
          background: `linear-gradient(135deg, ${theme.primary})`,
          boxShadow: `0 2px 6px ${theme.light}`
        }}
      >
        {IconComponent && <IconComponent className="h-3.5 w-3.5 text-white" />}
      </div>
      <span className="text-sm font-medium text-slate-700 flex-1 line-clamp-1 leading-relaxed">{feature.text}</span>
    </motion.div>
  )
}

const StatsSection = ({ price, theme }: { price?: string; theme: ThemeColors }) => (
  <div className="flex items-center justify-between p-3 rounded-xl bg-white/80 backdrop-blur-sm border border-white/50 shadow-sm">
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1">
        <Star className="w-4 h-4 text-yellow-500 fill-current drop-shadow-sm" />
        <span className="text-sm font-bold text-slate-700">4.9</span>
      </div>
      <span className="text-xs text-slate-500 font-medium">评分</span>
    </div>
    {price && (
      <div className="text-right">
        <div className="text-sm font-bold" style={{ color: theme.accent }}>
          {price}
        </div>
        <div className="text-xs text-slate-500">起</div>
      </div>
    )}
  </div>
)

const ActionButton = ({
  slug,
  theme,
  label,
}: {
  slug: string
  theme: ThemeColors
  label: string
}) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    transition={{ duration: 0.2 }}
  >
    <Button
      asChild
      className="w-full h-10 rounded-xl font-semibold text-white shadow-lg border-0 text-sm relative overflow-hidden group"
      style={{
        background: `linear-gradient(135deg, ${theme.primary})`,
        boxShadow: `0 4px 16px ${theme.light}, 0 2px 4px rgba(0,0,0,0.1)`
      }}
    >
      <Link href={`/products/${slug}`} className="flex items-center justify-center gap-2 relative z-10">
        <span>{label}</span>
        <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
        {/* Hover shine effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500" />
      </Link>
    </Button>
  </motion.div>
)

// Main component
export function ProductCardCompact({ product, index }: ProductCardProps) {
  const t = useTranslations("productsPage")
  const tCard = useTranslations("productsPage.card")
  const [isHovered, setIsHovered] = useState(false)
  const theme = useTheme(product.category)

  const cardStyle = useMemo(
    () => ({
      background: `linear-gradient(135deg, ${theme.cardBg})`,
      borderColor: isHovered ? theme.accent + "60" : "rgba(255, 255, 255, 0.4)",
      boxShadow: isHovered
        ? `0 12px 40px ${theme.light}, 0 4px 16px rgba(0,0,0,0.1)`
        : "0 6px 24px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0,0,0,0.04)",
    }),
    [theme, isHovered],
  )

  const topGradientStyle = useMemo(
    () => ({
      background: `linear-gradient(90deg, ${theme.primary})`,
      boxShadow: `0 2px 8px ${theme.light}`,
    }),
    [theme.primary, theme.light],
  )

  return (
    <motion.div
      className="group relative h-full"
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      transition={{
        duration: 0.5,
        delay: index * 0.1,
        type: "spring",
        stiffness: 100,
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div
        className={cn(
          "relative h-full rounded-2xl backdrop-blur-xl overflow-hidden",
          "border-2 transition-all duration-500 ease-out",
          "shadow-lg group-hover:shadow-2xl",
          "bg-gradient-to-br",
        )}
        style={cardStyle}
      >
        {/* Top gradient bar */}
        <div className="absolute top-0 left-0 right-0 h-1.5 opacity-95" style={topGradientStyle} />

        {/* Card content */}
        <div className="relative z-10 p-5 h-full flex flex-col">
          {/* Header section */}
          <div className="mb-5">
            {/* Category tag at top right */}
            <div className="flex justify-end mb-3">
              {product.category && <CategoryTag category={product.category} theme={theme} />}
            </div>

            <div className="flex items-start gap-4">
              <ProductIcon iconName={product.iconName} theme={theme} />

              <div className="flex-1 min-w-0 overflow-hidden">
                {/* Product name with full display - allow wrapping for long names */}
                <motion.h3
                  className="text-base sm:text-lg font-bold text-slate-900 leading-snug mb-3 break-words hyphens-auto"
                  style={{
                    wordBreak: 'break-word',
                    overflowWrap: 'break-word',
                    lineHeight: '1.3'
                  }}
                  variants={titleVariants}
                  whileHover="hover"
                >
                  {product.name}
                </motion.h3>

                <p className="text-sm text-slate-600 leading-relaxed line-clamp-2">{product.description}</p>
              </div>
            </div>
          </div>

          {/* Features section */}
          <div className="mb-4 flex-grow">
            <div className="space-y-2.5">
              {product.features.slice(0, 2).map((feature, featureIndex) => (
                <FeatureItem key={feature.text} feature={feature} theme={theme} index={featureIndex} />
              ))}
            </div>
          </div>

          {/* Stats section */}
          <div className="mb-4">
            <StatsSection price={product.price} theme={theme} />
          </div>

          {/* Action button */}
          <div className="mt-auto">
            <ActionButton slug={product.slug} theme={theme} label={tCard("learnMore")} />
          </div>
        </div>
      </div>
    </motion.div>
  )
}
