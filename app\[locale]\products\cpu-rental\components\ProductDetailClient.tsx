'use client'

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useTranslations } from "@/hooks/useTranslations"
import {
  ArrowRight,
  CheckCircle,
  Star,
  Shield,
  Zap,
  Globe,
  Users,
  Cpu,
  Database,
  BarChart,
  MessageCircle,
  Calendar,
  Download,
  Server,
  Activity
} from "lucide-react"
import Link from "next/link"

interface ProductDetail {
  name: string
  description: string
  features: string[]
  techSpecs: {
    deployment: string
    security: string
    availability: string
    support: string
  }
  featureList?: Array<{
    title: string
    description: string
    features: Array<{
      name: string
      description: string
      icon: string
    }>
  }>
  demoVideo?: {
    url: string
    thumbnail: string
  }
  benefits?: Array<{
    title: string
    description: string
  }>
}

interface Props {
  product: ProductDetail
  productSlug: string
}

const iconMap = {
  Cpu,
  Database,
  BarChart,
  Shield,
  Zap,
  Globe,
  Users,
  CheckCircle,
  Star,
  Server,
  Activity
}

export function ProductDetailClient({ product, productSlug }: Props) {
  const t = useTranslations('productDetails.cpuRental')
  const tCommon = useTranslations('common')

  const renderIcon = (iconName: string, className?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    return IconComponent ? <IconComponent className={className} /> : <Cpu className={className} />
  }

  return (
    <div className="relative isolate min-h-screen">
      {/* Background Effects */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-emerald-500/3 to-transparent" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-emerald-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Hero Section */}
      <section className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-4xl text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-green-500/20 bg-green-500/5">
                <Cpu className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                {product.name}
              </span>
            </motion.h1>

            <motion.p
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {product.description}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Button asChild size="lg" className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
                <Link href="/contact-us">
                  {tCommon('contactUs')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              
              <Button variant="outline" size="lg">
                <Calendar className="mr-2 h-5 w-5" />
                {tCommon('learnMore')}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('coreFeatures')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('coreDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {product.features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <CheckCircle className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold group-hover:text-green-600 transition-colors">
                        {feature}
                      </h3>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Performance Stats */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('performanceMetrics')}</h2>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 shadow-lg mb-4 inline-block">
                    <Server className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-green-600 mb-2">99.9%</h3>
                  <p className="text-sm text-muted-foreground">{t('serviceAvailability')}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 shadow-lg mb-4 inline-block">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-green-600 mb-2">5分钟</h3>
                  <p className="text-sm text-muted-foreground">{t('quickDeployment')}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 shadow-lg mb-4 inline-block">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-green-600 mb-2">60%</h3>
                  <p className="text-sm text-muted-foreground">{t('costSavings')}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 shadow-lg mb-4 inline-block">
                    <Globe className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-green-600 mb-2">全球</h3>
                  <p className="text-sm text-muted-foreground">{t('multiRegionDeployment')}</p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-green-500/5 to-emerald-500/10">
        <div className="mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('readyToStart')}</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              {t('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
                <MessageCircle className="w-5 h-5 mr-2" />
                {tCommon('contactUs')}
              </Button>
              <Button variant="outline" size="lg">
                <Download className="w-5 h-5 mr-2" />
                下载产品手册
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
