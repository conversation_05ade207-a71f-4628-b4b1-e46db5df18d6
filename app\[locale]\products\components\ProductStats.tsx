'use client'

import { motion, useInView } from "framer-motion"
import { TrendingUp, Users, Database, Award, ArrowUpRight, Sparkles } from "lucide-react"
import { useTranslations } from "@/hooks/useTranslations"
import { useRef, useEffect, useState } from "react"

export function ProductStats() {
  const t = useTranslations('productsPage.productStats')
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const stats = [
    {
      icon: Database,
      label: t('dataProcessing.label'),
      value: "10TB+",
      description: t('dataProcessing.description'),
      trend: "+25%",
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50"
    },
    {
      icon: Users,
      label: t('customers.label'),
      value: "2000+",
      description: t('customers.description'),
      trend: "+40%",
      color: "from-green-500 to-emerald-500",
      bgColor: "from-green-50 to-emerald-50"
    },
    {
      icon: TrendingUp,
      label: t('availability.label'),
      value: "99.9%",
      description: t('availability.description'),
      trend: t('availability.trend'),
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-50 to-pink-50"
    },
    {
      icon: Award,
      label: t('certifications.label'),
      value: "15+",
      description: t('certifications.description'),
      trend: "+3",
      color: "from-orange-500 to-red-500",
      bgColor: "from-orange-50 to-red-50"
    }
  ]

  // 数字动画计数器
  const useCountUp = (end: string, duration: number = 2000) => {
    const [count, setCount] = useState(0)
    const [displayValue, setDisplayValue] = useState("0")

    useEffect(() => {
      if (!isInView) return

      const numericValue = parseInt(end.replace(/[^\d]/g, ''))
      const suffix = end.replace(/[\d]/g, '')

      let startTime: number
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime
        const progress = Math.min((currentTime - startTime) / duration, 1)

        const currentCount = Math.floor(progress * numericValue)
        setCount(currentCount)
        setDisplayValue(currentCount + suffix)

        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }

      requestAnimationFrame(animate)
    }, [end, duration, isInView])

    return displayValue
  }
  return (
    <section ref={ref} className="py-20 sm:py-28 relative overflow-hidden">
      {/* 增强的背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-50/30" />

      {/* 动态背景元素 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl opacity-20 animate-pulse" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.15))' }} />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full blur-3xl opacity-15 animate-pulse" style={{ background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(59, 130, 246, 0.1))', animationDelay: '3s' }} />
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.1) 1px, transparent 0)`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 重新设计的标题 */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          viewport={{ once: true }}
        >
          {/* 标签徽章 */}
          <motion.div
            className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full backdrop-blur-md border mb-8 shadow-lg"
            style={{
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderColor: 'rgba(59, 130, 246, 0.3)',
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.15)'
            }}
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <TrendingUp className="w-4 h-4" style={{ color: 'rgb(59 130 246)' }} />
            <span className="text-sm font-semibold" style={{ color: 'rgb(59 130 246)' }}>实时数据</span>
          </motion.div>

          <motion.h2
            className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('title')}
          </motion.h2>

          <motion.p
            className="text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            {t('subtitle')}
          </motion.p>
        </motion.div>

        {/* 重新设计的统计数据网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-10">
          {stats.map((stat, index) => {
            const StatIcon = stat.icon

            return (
              <motion.div
                key={stat.label}
                className="group relative"
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 0.8 + index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* 主卡片 */}
                <div className={`relative p-8 h-full rounded-3xl bg-gradient-to-br ${stat.bgColor} border border-white/60 backdrop-blur-sm shadow-lg group-hover:shadow-2xl transition-all duration-500 overflow-hidden text-center`}>

                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/30 to-transparent rounded-full blur-2xl" />

                  {/* 图标容器 */}
                  <motion.div
                    className="relative mx-auto mb-6"
                    whileHover={{ rotate: [0, -10, 10, 0], scale: 1.1 }}
                    transition={{ duration: 0.6 }}
                  >
                    <div className="relative w-16 h-16 mx-auto">
                      {/* 图标背景光晕 */}
                      <div
                        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${stat.color} opacity-20 group-hover:opacity-30 transition-all duration-500 blur-lg`}
                      />

                      {/* 图标主体 */}
                      <div className={`relative flex items-center justify-center w-full h-full rounded-2xl bg-gradient-to-br ${stat.color} shadow-lg group-hover:shadow-xl transition-all duration-500`}>
                        <StatIcon className="w-8 h-8 text-white relative z-10" />
                      </div>

                      {/* 外部光环 */}
                      <div
                        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-40 transition-opacity duration-500 -z-10 blur-xl scale-110`}
                      />
                    </div>
                  </motion.div>

                  {/* 数值区域 */}
                  <div className="relative z-10 mb-6">
                    <motion.div
                      className="text-4xl lg:text-5xl font-bold text-gradient-modern mb-3 group-hover:scale-105 transition-transform duration-300"
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{
                        duration: 0.8,
                        delay: 1.2 + index * 0.1,
                        type: "spring",
                        stiffness: 200
                      }}
                      viewport={{ once: true }}
                    >
                      {stat.value}
                    </motion.div>

                    <div className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-slate-900 transition-colors duration-300">
                      {stat.label}
                    </div>

                    <div className="text-sm text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                      {stat.description}
                    </div>
                  </div>

                  {/* 趋势指示器 */}
                  <motion.div
                    className="relative z-10 flex items-center justify-center gap-2 px-4 py-2 rounded-xl bg-white/60 backdrop-blur-sm border border-white/40"
                    whileHover={{ scale: 1.05 }}
                  >
                    <ArrowUpRight className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-semibold text-green-600">
                      {stat.trend}
                    </span>
                  </motion.div>

                  {/* 底部装饰线条 */}
                  <div
                    className={`absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                  />

                  {/* 悬停时的边框光效 */}
                  <div
                    className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 -z-10 blur-xl`}
                  />
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* 重新设计的底部说明 */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.6 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-4 px-8 py-5 rounded-2xl bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-md border border-white/60 shadow-xl"
            style={{ boxShadow: '0 20px 60px rgba(59, 130, 246, 0.15)' }}
            whileHover={{ scale: 1.05, y: -2 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {/* 用户头像组 */}
            <div className="flex -space-x-3">
              {[
                { bg: 'from-blue-500 to-cyan-500' },
                { bg: 'from-purple-500 to-pink-500' },
                { bg: 'from-green-500 to-emerald-500' },
                { bg: 'from-orange-500 to-red-500' }
              ].map((avatar, i) => (
                <motion.div
                  key={i}
                  className={`w-10 h-10 rounded-full border-3 border-white bg-gradient-to-br ${avatar.bg} shadow-lg flex items-center justify-center`}
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{
                    duration: 0.5,
                    delay: 1.8 + i * 0.1,
                    type: "spring",
                    stiffness: 200
                  }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1, zIndex: 10 }}
                >
                  <span className="text-white text-xs font-bold">
                    {String.fromCharCode(65 + i)}
                  </span>
                </motion.div>
              ))}
            </div>

            {/* 分隔线 */}
            <div className="w-px h-8 bg-slate-200"></div>

            {/* 文本内容 */}
            <div className="text-left">
              <div className="font-semibold text-slate-800 mb-1">
                {t('bottomText.main')}
              </div>
              <div className="text-sm text-slate-600">
                {t('bottomText.sub')}
              </div>
            </div>

            {/* 状态指示器 */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                <div className="absolute inset-0 w-2 h-2 rounded-full bg-green-500 animate-ping opacity-75"></div>
              </div>
              <span className="text-xs font-medium text-green-600">实时更新</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
