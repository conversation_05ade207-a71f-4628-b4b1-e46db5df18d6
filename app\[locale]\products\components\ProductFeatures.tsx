'use client'

import { motion } from "framer-motion"
import { Shield, Zap, Users, Globe, Clock, Award, ArrowRight, CheckCircle } from "lucide-react"
import { useTranslations } from "@/hooks/useTranslations"

export function ProductFeatures() {
  const t = useTranslations('productsPage.productFeatures')

  const features = [
    {
      icon: Shield,
      title: t('security.title'),
      description: t('security.description'),
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50",
      shadowColor: "rgba(59, 130, 246, 0.25)"
    },
    {
      icon: Zap,
      title: t('performance.title'),
      description: t('performance.description'),
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-50 to-pink-50",
      shadowColor: "rgba(147, 51, 234, 0.25)"
    },
    {
      icon: Users,
      title: t('team.title'),
      description: t('team.description'),
      color: "from-green-500 to-emerald-500",
      bgColor: "from-green-50 to-emerald-50",
      shadowColor: "rgba(34, 197, 94, 0.25)"
    },
    {
      icon: Globe,
      title: t('global.title'),
      description: t('global.description'),
      color: "from-orange-500 to-red-500",
      bgColor: "from-orange-50 to-red-50",
      shadowColor: "rgba(249, 115, 22, 0.25)"
    },
    {
      icon: Clock,
      title: t('delivery.title'),
      description: t('delivery.description'),
      color: "from-indigo-500 to-blue-500",
      bgColor: "from-indigo-50 to-blue-50",
      shadowColor: "rgba(99, 102, 241, 0.25)"
    },
    {
      icon: Award,
      title: t('certification.title'),
      description: t('certification.description'),
      color: "from-yellow-500 to-orange-500",
      bgColor: "from-yellow-50 to-orange-50",
      shadowColor: "rgba(245, 158, 11, 0.25)"
    }
  ]
  return (
    <section className="py-20 sm:py-28 relative overflow-hidden">
      {/* 增强的背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20" />

      {/* 动态背景元素 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full blur-3xl opacity-20 animate-pulse" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.2))' }} />
        <div className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full blur-3xl opacity-15 animate-pulse" style={{ background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(59, 130, 246, 0.15))', animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] rounded-full blur-3xl opacity-10 animate-pulse" style={{ background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)', animationDelay: '4s' }} />
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.1) 1px, transparent 0)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 重新设计的标题区域 */}
        <motion.div
          className="mx-auto max-w-4xl text-center mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          viewport={{ once: true }}
        >
          {/* 标签徽章 */}
          <motion.div
            className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full backdrop-blur-md border mb-8 shadow-lg"
            style={{
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderColor: 'rgba(59, 130, 246, 0.3)',
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.15)'
            }}
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <CheckCircle className="w-4 h-4" style={{ color: 'rgb(59 130 246)' }} />
            <span className="text-sm font-semibold" style={{ color: 'rgb(59 130 246)' }}>{t('sectionTitle')}</span>
          </motion.div>

          {/* 主标题 */}
          <motion.h2
            className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('title')}
          </motion.h2>

          {/* 副标题 */}
          <motion.p
            className="text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            {t('subtitle')}
          </motion.p>
        </motion.div>

        {/* 重新设计的特性网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              className="group relative"
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.6,
                delay: 0.8 + index * 0.1,
                type: "spring",
                stiffness: 100
              }}
              viewport={{ once: true }}
              whileHover={{ y: -8, scale: 1.02 }}
            >
              {/* 主卡片 */}
              <div className={`relative p-8 h-full rounded-3xl bg-gradient-to-br ${feature.bgColor} border border-white/60 backdrop-blur-sm shadow-lg group-hover:shadow-2xl transition-all duration-500 overflow-hidden text-center`}>

                {/* 背景装饰 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/30 to-transparent rounded-full blur-2xl" />

                {/* 图标容器 */}
                <motion.div
                  className="relative mx-auto mb-6"
                  whileHover={{ rotate: [0, -10, 10, 0], scale: 1.1 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="relative w-20 h-20 mx-auto">
                    {/* 图标背景光晕 */}
                    <div
                      className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.color} opacity-20 group-hover:opacity-30 transition-all duration-500 blur-lg`}
                    />

                    {/* 图标主体 */}
                    <div className={`relative flex items-center justify-center w-full h-full rounded-2xl bg-gradient-to-br ${feature.color} shadow-lg group-hover:shadow-xl transition-all duration-500`}>
                      <feature.icon className="w-10 h-10 text-white relative z-10" />
                    </div>

                    {/* 外部光环 */}
                    <div
                      className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-40 transition-opacity duration-500 -z-10 blur-xl scale-110`}
                    />
                  </div>
                </motion.div>

                {/* 内容区域 */}
                <div className="relative z-10">
                  <h3 className="text-xl font-bold text-slate-800 mb-4 group-hover:text-slate-900 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600 leading-relaxed mb-6 group-hover:text-slate-700 transition-colors duration-300">
                    {feature.description}
                  </p>

                  {/* 了解更多按钮 */}
                  <motion.div
                    className="inline-flex items-center gap-2 text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300"
                    style={{ color: feature.color.includes('blue') ? 'rgb(59 130 246)' : 'rgb(147, 51, 234)' }}
                    whileHover={{ x: 5 }}
                  >
                    <span>了解更多</span>
                    <ArrowRight className="w-4 h-4" />
                  </motion.div>
                </div>

                {/* 底部装饰线条 */}
                <div
                  className={`absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                />

                {/* 悬停时的边框光效 */}
                <div
                  className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 -z-10 blur-xl`}
                />
              </div>
            </motion.div>
          ))}
        </div>

        {/* 重新设计的底部CTA */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.4 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-3 px-8 py-4 rounded-2xl bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-md border border-white/60 shadow-xl"
            style={{ boxShadow: '0 20px 60px rgba(59, 130, 246, 0.15)' }}
            whileHover={{ scale: 1.05, y: -2 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {/* 状态指示器 */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 rounded-full bg-green-500 animate-ping opacity-75"></div>
              </div>
              <span className="text-xs font-medium text-green-600 uppercase tracking-wide">在线服务</span>
            </div>

            {/* 分隔线 */}
            <div className="w-px h-6 bg-slate-200"></div>

            {/* 主要信息 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-semibold text-slate-700">
                {t('bottomCta', { count: '2000+' })}
              </span>
              <ArrowRight className="w-4 h-4 text-blue-500" />
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
